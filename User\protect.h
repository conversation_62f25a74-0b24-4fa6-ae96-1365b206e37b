/*
 * protect.h
 *
 *  Created on: 2024年2月19日
 *      Author: adam
 */

#ifndef SOURCE_PROTECT_H_
#define SOURCE_PROTECT_H_

#include "common.h"



enum protect_code_t
{
    PROT_WORD_0 = 32 * 0, // 立即保护
    PROT_PowerOff,        // 关机
    PROT_Ia_Over,
    PROT_Ib_Over,
    PROT_Ic_Over, //
    PROT_Ipv1_Over,
    PROT_Ipv2_Over, //

    PROT_Grid_Ua_Over,
    PROT_Grid_Ua_Under, //
    PROT_Grid_Ub_Over,
    PROT_Grid_Ub_Under, //
    PROT_Grid_Uc_Over,
    PROT_Grid_Uc_Under, //
    PROT_Grid_Freq_Over,
    PROT_Grid_Freq_Under, //

    PROT_WORD_1 = 32 * 1, // 立即保护
    PROT_Vpv1_Over,
    PROT_Vpv1_Under,
    PROT_Vpv2_Over,
    PROT_Vpv2_Under,
    PROT_VBusBalance_Over,
    PROT_VBusBalance_Under,
    PROT_VBusSum_Over,
    PROT_VBusSum_Under, // 40

    PROT_PV_Power_Over,           // 直流输入过载保护 50
    PROT_PV_underpower,           // 太阳能功率不足
    PROT_unintentional_islanding, // 非计划孤岛

    PROT_WORD_2 = 32 * 2, //
    PROT_WORD_3 = 32 * 3, //
    PROT_WORD_4 = 32 * 4, //
    PROT_WORD_5 = 32 * 5, //
    PROT_WORD_6 = 32 * 6, //
    PROT_WORD_7 = 32 * 7, //

    PROT_ERROR_MAX = 0x7FFFFFFF,
};

typedef struct Protect_Handle_t
{
    enum protect_code_t protect_code;
    uint32_t protect_word[10];
    uint32_t protect_flag;
    const char *text_label;
} Protect_Handle_t;

typedef struct Protect_Member_t // 保护
{
    enum _ActFlag
    {
        PROT_DISABLE, // 不保护
        PROT_NORMAL,  // 正常normal
        PROT_OVER,    // 过上限
        PROT_UNDER    // 过下限
    } ActFlag;        // 触发标志

    float ActValue;          // 触发值
    float ActUpperThreshold; // 上限位动作阈值
    float ActLowerThreshold; // 下限位动作阈值
    uint32_t ActDelay;       // 确认保护时间
    uint32_t ExtDelay;       // 退出保护时间
    uint32_t ActCount;       // 确认时间计数
    uint32_t ExtCount;       // 退出时间计数
    char *OverLabel;         // 文本标签
    char *UnderLabel;        // 文本标签

    enum protect_code_t over_prot_code;
    enum protect_code_t under_prot_code;

} Protect_Member_t; // 成员

void Protect_Member_Init(Protect_Member_t *hProt,                                                    //
                         float ActUpperThreshold, float ActLowerThreshold,                           //
                         uint32_t ActDelay, uint32_t ExtDelay,                                       //
                         enum protect_code_t Upper_error_cord, enum protect_code_t Lower_error_cord, //
                         char *OverLabel, char *UnderLabel);                                         //

void Protect_Member_Check(Protect_Member_t *hProt, float Value);

void protect_set_error_code(enum protect_code_t error_code, const char *text);
void protect_reset_error_code(enum protect_code_t error_cord);

#define PROTECT_CURRENT_CHECK_MACRO(hProt, Val)                                    \
    {                                                                              \
        float Value = (Val);                                                       \
        if (hProt.ActFlag == PROT_NORMAL)                                          \
        {                                                                          \
            if (Value > hProt.ActUpperThreshold)                                   \
            {                                                                      \
                hProt.ActCount++;                                                  \
                if (hProt.ActCount >= hProt.ActDelay)                              \
                {                                                                  \
                    protect_set_error_code(hProt.over_prot_code, hProt.OverLabel); \
                    hProt.ActCount = 0;                                            \
                    hProt.ActFlag = PROT_OVER;                                     \
                    hProt.ActValue = Value;                                        \
                }                                                                  \
            }                                                                      \
            else                                                                   \
            {                                                                      \
                hProt.ActCount = 0;                                                \
            }                                                                      \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            if (Value < hProt.ActLowerThreshold)                                   \
            {                                                                      \
                hProt.ExtCount++;                                                  \
                if (hProt.ExtCount >= hProt.ExtDelay)                              \
                {                                                                  \
                    protect_reset_error_code(hProt.over_prot_code);                \
                    hProt.ExtCount = 0;                                            \
                    hProt.ActFlag = PROT_NORMAL;                                   \
                }                                                                  \
            }                                                                      \
            else                                                                   \
            {                                                                      \
                hProt.ActValue = Value;                                            \
                hProt.ExtCount = 0;                                                \
            }                                                                      \
        }                                                                          \
    }

#define PROTECT_MEMBER_CHECK_MACRO(h, Val)                                       \
    {                                                                            \
        float Value = (Val);                                                     \
        if (h.ActFlag == PROT_NORMAL)                                            \
        {                                                                        \
            if (Value > h.ActUpperThreshold || Value < h.ActLowerThreshold)      \
            {                                                                    \
                h.ActCount++;                                                    \
                if (h.ActCount >= h.ActDelay)                                    \
                {                                                                \
                    if (Value > h.ActUpperThreshold)                             \
                    {                                                            \
                        protect_set_error_code(h.over_prot_code, h.OverLabel);   \
                        h.ActFlag = PROT_OVER;                                   \
                    }                                                            \
                    else                                                         \
                    {                                                            \
                        protect_set_error_code(h.under_prot_code, h.UnderLabel); \
                        h.ActFlag = PROT_UNDER;                                  \
                    }                                                            \
                    h.ActValue = Value;                                          \
                    h.ActCount = 0;                                              \
                }                                                                \
            }                                                                    \
            else                                                                 \
            {                                                                    \
                h.ActCount = 0;                                                  \
            }                                                                    \
        }                                                                        \
        else if (h.ActFlag == PROT_DISABLE)                                      \
        {                                                                        \
            h.ActValue = Value;                                                  \
        }                                                                        \
        else                                                                     \
        {                                                                        \
            if (Value < h.ActUpperThreshold && Value >= h.ActLowerThreshold)     \
            {                                                                    \
                h.ExtCount++;                                                    \
                if (h.ExtCount >= h.ExtDelay)                                    \
                {                                                                \
                    h.ExtCount = 0;                                              \
                    h.ActFlag = PROT_NORMAL;                                     \
                                                                                 \
                    protect_reset_error_code(h.over_prot_code);                  \
                    protect_reset_error_code(h.under_prot_code);                 \
                }                                                                \
            }                                                                    \
            else                                                                 \
            {                                                                    \
                h.ActValue = Value;                                              \
                h.ExtCount = 0;                                                  \
            }                                                                    \
        }                                                                        \
    }

#endif /* SOURCE_PROTECT_H_ */
